namespace :permissions do
  desc "为现有的子企业生成继承权限"
  task generate_inherited: :environment do
    puts "开始为现有的子企业生成继承权限..."
    
    # 查找所有有父企业的组织
    child_organizations = Organization.where.not(parent_id: nil)
    
    child_organizations.find_each do |organization|
      puts "正在为企业 '#{organization.name}' (ID: #{organization.id}) 生成继承权限..."
      
      begin
        organization.generate_inherited_permissions
        puts "  ✓ 成功为企业 '#{organization.name}' 生成继承权限"
      rescue => e
        puts "  ✗ 为企业 '#{organization.name}' 生成继承权限时出错: #{e.message}"
      end
    end
    
    puts "权限继承生成完成！"
  end

  desc "验证所有企业的权限分配是否合法"
  task validate_permissions: :environment do
    puts "开始验证所有企业的权限分配..."
    
    invalid_permissions = []
    
    RolePermission.includes(:role, :permission_action, :organization).find_each do |role_permission|
      organization = role_permission.role.organization
      
      unless organization.can_assign_permission_action?(role_permission.permission_action_id)
        invalid_permissions << {
          organization: organization.name,
          role: role_permission.role.name,
          permission: role_permission.permission_action.name,
          controller: role_permission.permission_action.permission_controller.name
        }
      end
    end
    
    if invalid_permissions.empty?
      puts "✓ 所有权限分配都是合法的"
    else
      puts "✗ 发现 #{invalid_permissions.count} 个非法权限分配:"
      invalid_permissions.each do |invalid|
        puts "  - 企业: #{invalid[:organization]}, 角色: #{invalid[:role]}, 权限: #{invalid[:controller]} -> #{invalid[:permission]}"
      end
    end
  end

  desc "清理非法的权限分配"
  task cleanup_invalid_permissions: :environment do
    puts "开始清理非法的权限分配..."
    
    cleaned_count = 0
    
    RolePermission.includes(:role, :permission_action).find_each do |role_permission|
      organization = role_permission.role.organization
      
      unless organization.can_assign_permission_action?(role_permission.permission_action_id)
        puts "  删除非法权限分配: 企业 '#{organization.name}' 的角色 '#{role_permission.role.name}' 的权限 '#{role_permission.permission_action.name}'"
        role_permission.destroy
        cleaned_count += 1
      end
    end
    
    puts "清理完成，共删除 #{cleaned_count} 个非法权限分配"
  end
end
