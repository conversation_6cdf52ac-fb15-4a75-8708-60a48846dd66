
### 组织: organizations
```
acts_as_paranoid

belongs_to: parent_organization, foreign_key: :parent_organization_id, class_name: :Organization
has_many :child_organizations, foreign_key: :parent_organization_id, class_name: :Organization

validates :name, :email, presence: true
```

| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name              | string      | 名称                     |
| address             | string      | 公司地址                 |
| logo             | string      | 公司LOGO                 |
| email              | string      | 公司邮箱                   |
| parent_id   | integer     | 自关联             |
| deleted_at   | datetime     | 软删除、删除时间           |


### 组织的流程配置表: organization_flows
```
acts_as_paranoid
belongs_to :organization
has_many :flow_versions, dependent: :destroy

validates :name, :flow_type, presence: true

```

| Column Name       | Type        | Description                          |
|-------------------|-------------|--------------------------------------|
| id                | integer     | 主键，自增                           |
| name              | string      | 流程名称          |
| description       | text        | 流程描述                             |
| flow_type         | integer     | 使用给的流程      |
| deleted_at   | datetime     | 软删除、删除时间           |
| organization_id   | integer     | 组织ID             |

### 组织的流程版本表: flow_versions
```
acts_as_paranoid
belongs_to :organization_flow
belongs_to :organization

has_many :flow_steps
```
| Column Name       | Type        | Description                          |
|-------------------|-------------|--------------------------------------|
| id                | integer     | 自增ID                               |
| version           | integer     | 版本号                               |
| status            | boolean     | 启用状态                               |
| user_id           | integer     | 创建人                               |
| organization_flow_id| integer   | 组织流程ID                               |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |


### 审核步骤表: flow_steps
```
acts_as_paranoid
belongs_to: flow_version
validates :order_number, :name, presence: true
```
| Column Name       | Type        | Description                          |
|-------------------|-------------|--------------------------------------|
| id                | integer     | 主键，自增                           |
| flow_version_id | integer   | 外键，关联到组织的流程版本表               |
| order_number             | integer     | 步骤顺序（如第1步、第2步）           |
| name         | string      | 步骤名称（如“初审”、“终审”）         |
| review_type       | integer      | 审核类型（1: 会签（需所有审批人同意)、2: 或签（一名审批人同意即可）、3: 依次审批（按顺序依次审批）） |
| deleted_at   | datetime     | 软删除、删除时间           |
| organization_id   | integer     | 组织ID             |
| review_user_ids   | string     | 审核人员          |

### 审核流程表: approval_flow
```
acts_as_paranoid
belongs_to :organization_flow
belongs_to :flowable, polymorphic: true
has_many :approval_steps, dependent: :destroy

validates :status, presence: true
```
| Column Name       | Type        | Description                          |
|-------------------|-------------|--------------------------------------|
| id                | integer     | 主键，自增                           |
| organization_flow_id | integer  | 外键，关联到审核流程表               |
| flow_version_id | integer  | 外键，关联到审核流程版本表               |
| flowable_id       | integer     | 目标资源ID、多态ID              |
| flowable_type     | string      | 目标资源类型、多态类型        |
| current_step_id   | integer     | 当前所在步骤                         |
| status            | integer      | 整体状态（1: "进行中", 2: "已完成", 3: "驳回"）     |
| deleted_at        | datetime    | 软删除、删除时间           |

### 审核步骤表: approval_steps
```
acts_as_paranoid
belongs_to :approval_flow
belongs_to :organization
has_many :approval_step_users, dependent: :destroy

validates :order_number, :name, :review_type, presence: true
```
| Column Name       | Type        | Description                          |
|-------------------|-------------|--------------------------------------|
| approval_flow_id | integer      | 外键，关联到审核流程表               |
| order_number             | integer     | 步骤顺序         |
| name         | string           | 步骤名称        |
| review_type       | integer     | 审核类型（1: 会签（需所有审批人同意)、2: 或签（一名审批人同意即可）、3: 依次审批（按顺序依次审批）） |
| deleted_at        | datetime     | 软删除、删除时间           |
| organization_id   | integer     | 组织ID             |

### 审核步骤: approval_step_users
```
acts_as_paranoid
belongs_to :approval_step
belongs_to :user
belongs_to :organization

validates :order_number, :status, :comment, presence: true
```
| Column Name       | Type        | Description                          |
|-------------------|-------------|--------------------------------------|
| order_number      | integer     | 步骤顺序                              |
| user_id           | integer     | 审核人ID                             |
| approval_step_id  | integer     | 审核步骤ID                           |
| status            | integer      | 审核状态（1: "待审批", 2: "已同意", 3: "不同意"） |
| review_comment           | string      | 审核意见                             |
| deleted_at        | datetime    | 软删除、删除时间           |
| organization_id   | integer     | 组织ID             |

### 用户: users
```
acts_as_paranoid

belongs_to :organization
belongs_to :recommend_user, foreign_key: "recommend_user_id", class_name: "User", optional: true
has_many :user_roles, dependent: :destroy

validates :name, :email, :oaid, presence: true
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name              | string      | 姓名                     |
| email             | string      | 用户的邮箱地址                 |
| oaid              | string      | OA的关键值                   |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |
| avatar   | string     | 头像           |
| last_login_at   | datetime     | 最后登录时间           |
| password_digest   | string     | 密码           |
| phone   | string     | 手机号           |
| recommend_user_id   | integer     | 直属上级用户ID           |
| status   | integer     | 状态  1: 活跃 2: 冻结        |

### 角色: roles
```
acts_as_paranoid

belongs_to :organization
has_many :role_permissions, dependent: :destroy
has_many :permissions, through: :role_permissions
has_many :user_roles, dependent: :destroy
has_many :users, through: :user_roles

validates :name, presence: true
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name              | string      | 角色名称（如 admin, editor）   |
| description       | text        | 角色描述                       |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 权限: permissions
```
acts_as_paranoid

belongs_to :organization
has_many :role_permissions, dependent: :destroy

validates :action_name, :controller_name, presence: true
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| action_name       | string      | Action名称                     |
| controller_name   | string      | 控制器名称                 |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 角色权限关联表: role_permissions
```
acts_as_paranoid

belongs_to :organization
belongs_to :role
belongs_to :permission
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| role_id           | integer     | 角色ID                   |
| permission_id     | integer     | 权限ID             |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 用户角色关联表: user_roles
```
acts_as_paranoid

belongs_to :organization
belongs_to :user
belongs_to :role
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| user_id           | integer     | 用户ID                   |
| role_id           | integer     | 角色ID             |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 项目权限配置表: project_permission_configs
```
acts_as_paranoid

belongs_to :organization
has_many :user_role_permissions, dependent: :destroy
belongs_to :parent_project_permission_config, foreign_key: :parent_id, class_name: :ProjectPermissionConfig
has_many :child_project_permission_configs, foreign_key: :parent_id, class_name: :ProjectPermissionConfig, dependent: :destroy

validates :key, :name, presence: true
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| order_number           | integer     | 排序号                   |
| key                   | string      | 关键值（如：view, edit）     |
| name                   | string      | 名称（如：查看，编辑）     |
| parent_id               | integer     | 父级ID             |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 用户角色配置表: project_role_configs
```
acts_as_paranoid
belongs_to :organization

validates :name, presence: true
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name              | string      | 名称（如：管理员，编辑）   |
| is_default        | boolean     | 是否默认           |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 角色权限关联表: project_role_permissions
```
acts_as_paranoid

belongs_to :organization
belongs_to :project_permission_config
belongs_to :project_role_config
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| project_permission_config_id   | integer     | 项目权限配置ID             |
| project_role_config_id           | integer     | 用户角色配置ID             |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 项目用户角色关联表: project_users
```
acts_as_paranoid

belongs_to :organization
belongs_to :project
belongs_to :user
belongs_to :project_role_config

```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| user_id           | integer     | 用户ID                   |
| project_id        | integer     | 项目ID             |
| project_role_config_id | integer     | 项目角色配置ID             |
| organization_id   | integer     | 组织ID             |
| deleted_at   | datetime     | 软删除、删除时间           |

### 项目表: projects
rails g model projects name:string chip_platform:string project_type:integer product_name:string description:text main_purpose:string design_in_at:datetime evt_at:datetime dvt_at:datetime pvt_at:datetime mp_at:datetime status:integer fcst_per_month:'decimal{14,2}' mp_plus_six_months:'decimal{14,2}' specification_file:string user_id:integer:index deleted_at:datetime company_name:string company_type:integer terminal_customer_name:string os_version:string team_size:integer customer_project_name:string target_market_region:string agreement_accepted:boolean username:string phone:string email:string acceptor_name:string opening_at:datetime opening_desc:text main_competitiveness:string
```
acts_as_paranoid

belongs_to :organization
has_many :project_users, dependent: :destroy
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name              | string      | 项目名称   |
| chip_platform              | string      | 芯片平台   |
| project_type              | integer      | 项目类型   |
| product_caregoy_id              | integer      | 产品类型   |
| product_name              | string      | 产品名称   |
| description       | text        | 背景介绍                      |
| main_purpose      | string        | 项目目标、主要目的    |
| design_in_at   | datetime     | Design in 时间、立项时间           |
| evt_at   | datetime     | EVT时间、工程测试时间        |
| dvt_at   | datetime     | DVT时间、设计验证时间         |
| pvt_at   | datetime     | PVT时间、小批量过程验证时间       |
| mp_at    | datetime     | MP时间、批量生产时间       |
| status   | integer      | 状态           |
| fcst_per_month     | decimal      | FCST/月           |
| mp_plus_six_months | decimal      | MP+6月           |
| specification_file | string       | 规格附件           |
| user_id  | integer      | 用户ID、项目创建人             |
| deleted_at   | datetime     | 软删除、删除时间           |
|    |      |            |
| terminal_customer_name   | string     | 终端客户简称  |
| os_version   | string     | OS 系统版本  |
| team_size   | integer     | 项目人数  |
| customer_project_name   | string     | 客户项目简称 |
| target_market_region   | string     | 目标市场区域 |
| agreement_accepted   | boolean     | 是否同意协议 |
| username   | string     | 姓名 |
| phone   | string     | 联系电话 |
| email   | string     | 邮件 |
| acceptor_name   | string     | 业务受理人 |
| opening_at   | datetime     | 开案时间 |
| opening_desc   | datetime     | 开案说明 |
| main_competitiveness | string     | 产品主要功能 |

### 项目参与组织表: project_organizations
rails g model project_organizations project_id:integer:index organization_id:integer:index p_type:integer deleted_at:datetime
```
acts_as_paranoid

belongs_to :project
belongs_to :organization
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| project_id        | integer     | 项目ID             |
| organization_id   | integer     | 组织ID             |
| p_type            | integer     | 1: 组织者， 2: 参与者       |
| deleted_at   | datetime     | 软删除、删除时间           |

### 项目计划: plans
```
acts_as_paranoid
belongs_to :project
belongs_to :user
belongs_to :parent_plan, foreign_key: "parent_id", class_name: "Plan", optional: true
has_many :child_plans, foreign_key: "parent_id", class_name: "Plan", dependent: :destroy

validates :name, :begin_at, :end_at, :status, :p_type, presence: true
```
| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name              | string      | 计划名称                   |
| content           | string      | 计划内容                |
| started_at          | datetime      | 开始时间                   |
| ended_at            | datetime      | 结束时间                   |
| act_started_at            | datetime      | 实际开始时间                   |
| act_ended_at            | datetime      | 实际结束时间                   |
| status          | integer     | 任务状态             |
| p_type          | integer     | 任务类型， 1: 计划组，2: 计划 , 3: 里程碑            |
| p_priority      | integer     | 优先级  1:高 2：中 3: 低           |
| parent_id        | integer     | 项目ID             |
| project_id        | integer     | 项目ID             |
| user_id           | integer     | 用户ID             |
| duty_user_id           | integer     | 责任人用户ID    |
| deleted_at   | datetime     | 软删除、删除时间           |

### 芯片配置: chip_configs
```
acts_as_paranoid
belongs_to :organization
has_many :chip_os_versions, dependent: :destroy
```

| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| name                | string     | 芯片名称                         |
| deleted_at   | datetime     | 软删除、删除时间           |
| organization_id   | integer     | 组织ID             |

### 芯片型号: chip_os_versions
```
belong_to :chip_config
```

| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| version           | string     | 版本号                     |
| chip_config_id    | integer     | 芯片ID                     |
| deleted_at   | datetime     | 软删除、删除时间           |
| organization_id   | integer     | 组织ID             |

### 产品类型: product_category
```

belong_to :organization
has_many :projects

```

| Column Name       | Type        | Description                     |
|-------------------|-------------|---------------------------------|
| id                | integer     | 主键                           |
| name              | string      | 产品类型名称                   |
| organization_id   | integer     | 组织ID                         |
| deleted_at   | datetime     | 软删除、删除时间           |


rails g admin:scaffold_controller



步骤条:
```
<div class="step finished">
  <div class="circle">1</div>
  <div class="label">Step 1</div>
</div>

<div class="step in-progress">
  <div class="circle">2</div>
  <div class="label">Step 2</div>
</div>
<div class="step waiting">
  <div class="circle">3</div>
  <div class="label">Step 3</div>
</div>
```