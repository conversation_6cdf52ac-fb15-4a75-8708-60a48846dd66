# == Route Map
#

Rails.application.routes.draw do
  get '/admin' => "smart_alliance/law_admins#index"
  get '/' => "smart_alliance/law_admins#index"

  post "/upload_file" => "upload#upload_file", :as => :upload_file
  post "/upload_video" => "upload#upload_video", :as => :upload_video
  post "/upload_image" => "upload#upload_image", :as => :upload_image
  post "/upload_anyfile" => "upload#upload_anyfile", :as => :upload_anyfile

  get "/download_file/:name" => "upload#access_file", :as => :upload_access_file, :name => /.*/

  namespace :smart_alliance do
    resources :law_admins
  end

  namespace :web do
  end

  namespace :admin do
    resources :project_organizations
    resources :flow_steps
    resources :approval_steps, only: [:show]
    resources :flow_versions
    resources :organization_flows
    resources :plans do
      collection do
        post :update_sort
        post :import_excel
      end
    end

    resources :roles
    resources :project_users do
      collection do
        post :delete_role
        post :update_user_role
      end
    end
    resources :public_api do
      collection do
        get :get_chip_sofwares
        get :get_chip_versions
        get :search_users
        get :get_chip_configs
        get :search_project_users
      end
    end

    resources :work_orders do
      collection do
        get :evaluate_work_orders
        get :passing_work_orders
        get :project_bug_works
        get :project_demand_works
      end
      member do
        post :change_state
      end
    end
    resources :organizations
    resources :chip_os_versions
    resources :chip_os_softwares do
      get :get_version, on: :collection
    end

    resources :chip_configs
    resources :product_categories
    resources :projects do
      collection do
        get :new_joint
        get :new_support
        get :edit_joint
        get :edit_support
        post :update_status
        get :admin
      end
    end
    resources :project_role_configs do
      post :update_is_default, on: :collection
    end

    resources :users
    resources :welcomes do
      collection do
        get :approval_step_users_list
        post :update_approval_step_user
        get :current_week_data
        get :next_week_data
        post :update_plan_status
        post :update_work_order_status
        get :projects
      end
    end
    resources :sessions do
      collection do
        post :logout
        post :login_user
        get :login
        post :update_password
      end
    end
    resources :user_notices
  end
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Defines the root path route ("/")
  # root "articles#index"

  namespace :api, defaults: {format: :json} do
    namespace :v20240522 do
      resources :users
    end
  end
end
