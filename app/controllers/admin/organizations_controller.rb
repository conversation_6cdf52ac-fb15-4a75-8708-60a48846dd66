class Admin::OrganizationsController < Admin::ApplicationController
  before_action do
    authorize Organization
  end

  before_action :set_organization, only: [:show, :edit, :update, :destroy]

  # GET /admin/organizations
  def index
    @q = Organization.order(created_at: :desc).ransack(params[:q])
    @organizations = @q.result
    if request.xhr?
      @result = @organizations.page(params[:page]).per(params[:limit]).map do |organization|
        {
          id: organization.id,
          name: organization.name,
          org_type: organization.org_type_i18n,
          parent_id: organization.parent&.name
        }
      end
      render json: {code: 0, data: @result, count: @organizations.count}
    end
  end

  # GET /admin/organizations/1
  def show
  end

  # GET /admin/organizations/new
  def new
    @organization = Organization.new
  end

  # GET /admin/organizations/1/edit
  def edit
  end

  # POST /admin/organizations
  def create
    @organization = Organization.new(organization_params)
    if @organization.save
      @organization.users.create(
        name: params[:admin_user][:name],
        phone: params[:admin_user][:phone],
        password: params[:admin_user][:password],
        password_confirmation: params[:admin_user][:password],
        is_admin: true
      )
      @organization.generate_admin_auth

      @status = true
    else
      @status = false
      @msg = "失败了, #{@organization.errors.full_messages.join(',')}"
    end
  end

  # PATCH/PUT /admin/organizations/1
  def update
    if @organization.update(organization_params)
      @status = true
    else
      @status = false
      @msg = "失败了, #{@organization.errors.full_messages.join(',')}"
    end
  end

  # DELETE /admin/organizations/1
  def destroy
    if @organization.destroy
      @status = true
    else
      @status = false
      @msg = "失败了, #{@organization.errors.full_messages.join(',')}"
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_organization
      @organization = Organization.find(params[:id])
    end

    # Only allow a trusted parameter "white list" through.
    def organization_params
      params.require(:organization).permit(:name, :org_type, :parent_id)
    end
end
