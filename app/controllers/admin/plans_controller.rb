class Admin::PlansController < Admin::ApplicationController
  before_action :set_plan, only: [:show, :edit, :update, :destroy]
  before_action :set_project

  before_action do
    authorize Plan
  end

  # GET /admin/plans
  def index
    # 发布状态
    @show_release_plans = @project.show_release_plans
    # 当前用户拥有的权限
    @project_power_keys = @project.project_power_keys(current_user_id)

    if params[:excel]
      file = Plan.export_excel(@project)
      send_file file, filename: "#{@project.name}_计划.xlsx"
      return
    end

    if request.xhr?
      @plans = @project.plans.where(parent_id: nil).order(:order_number)
      result = @plans.map do |plan|
        plan.set_hash(current_user_id)
      end
      render json: {code: 0, data: result}
    end
  end

  # GET /admin/plans/1
  def show
  end

  # GET /admin/plans/new
  def new
    @plan = Plan.new(parent_id: params[:parent_id], user_id: current_user.id, organization_id: current_organization_id, project_id: params[:project_id], status: 'no_start', p_type: 'p_plan', p_priority: 'p_normal')
    @min_started_at = (Time.now - 200.years).beginning_of_day
    @max_ended_at = (Time.now + 200.years).end_of_day
    if params[:parent_id]
      parent_plan = Plan.find(params[:parent_id])
      @min_started_at = parent_plan.started_at
      @max_ended_at = parent_plan.ended_at
    end
    @parents = @project.plans.order(:order_number).pluck(:name, :id)
    @users = @project.current_users(current_organization_id).pluck(:name, :id)
  end

  # GET /admin/plans/1/edit
  def edit
    ids = @plan.child_plan_ids.push(@plan.id)
    @parents = @project.plans.where.not(id: ids).order(:order_number).pluck(:name, :id)
    @users = @project.current_users(current_organization_id).pluck(:name, :id)
    @min_started_at = (Time.now - 200.years).beginning_of_day
    @max_ended_at = (Time.now + 200.years).end_of_day
    if @plan.parent_plan
      @min_started_at = @plan.parent_plan.started_at
      @max_ended_at = @plan.parent_plan.ended_at
    end
  end

  # POST /admin/plans
  def create
    begin
      @status = true
      ActiveRecord::Base.transaction do
        @plan = Plan.new(plan_params)
        @status = @plan.save!
        @project = @plan.project

        # 创建用户通知
        create_user_notice

        sort_ids = params[:plan][:sort_ids].split(',')
        if sort_ids.present?
          if @plan.parent_id.present?
            sort_ids.each_with_index.reverse_each do |id, index|
              sort_ids.insert(index + 1, @plan.id) if id.to_i == @plan.parent_id
            end
          else
            sort_ids << @plan.id
          end
          update_order(sort_ids)
        end
      end
    rescue => exception
      Rails.logger.error exception
      @status = false
      @msg = "保存失败了, #{exception.message}"
    end
  end

  # PATCH/PUT /admin/plans/1
  def update
    begin
      @status = true
      @plan.update!(plan_params)
    rescue => exception
      @status = false
      Rails.logger.error exception.message
      @msg = "保存失败了, #{exception.message}"
    end

  end

  # DELETE /admin/plans/1
  def destroy
    begin
      @status = true
      ActiveRecord::Base.transaction do
        @plan.destroy!
        sort_ids = params[:sort_ids]
        sort_ids.delete(@plan.id.to_s)
        update_order(sort_ids)
      end
    rescue => exception
      Rails.logger.error exception
      @status = false
    end
  end

  def update_sort
    # @status = Plan.update_sort(params[:ids])
    # {"project_id"=>"1", "sort_ids"=>["1", "2", "3", "8", "9", "4", "10", "5", "6", "7"]}
    # 按照sort_ids 的顺序更新需要
    begin
      ActiveRecord::Base.transaction do
        update_order(params[:sort_ids])
      end
      render json: {status: true}
    rescue => exception
      Rails.logger.info "Error: #{exception.message}"
      render json: {status: false}
    end

  end

  def import_excel
    begin
      file_excel = params[:file]
      render json: {status: false, msg: '无文件导入' } and return if file_excel.blank?
      file_upload_path = File.expand_path(Rails.root.join("public/uploads/#{file_excel.original_filename}"))
      File.open(file_upload_path, 'wb') { |file| file.write(file_excel.read)}
      error_file, error_count = Plan.import_excel(file_upload_path, @project, current_user)

      @filename = "#{file_excel.original_filename}-导入错误信息#{Time.now.to_i}.xlsx"
      File.delete(file_upload_path)
      error_file = File.open(error_file)
      editor_file = error_count > 0 ? EditorFile.create(file: error_file, file_type: 'file_other', file_name: @filename) : nil
      render json: {status: true, msg: "导入成功", data: {error_count: error_count ,url: editor_file&.file_url, filename: @filename}}
    rescue => exception
      p exception.message
      render json: {status: false, msg: "文件格式错误"}
    end
  end

  private

    def update_order(sort_ids)
      sort_ids.each_with_index do |plan_id, index|
        # 找到对应的 plan 对象
        plan = @project.plans.find(plan_id)
        plan.update_columns(order_number: index + 1)
      end
    end

    def create_user_notice
      # 如果项目处于发布后的状态
      if @project.progressing?
        title = "项目计划创建通知"
        content = "您在项目 “#{@project.name}” 中有了新的计划: #{@plan.name}，请前往查看"
        url = "/admin/plans/#{@plan.id}"
        UserNotice.init_message!(current_organization_id, @plan.duty_user_id, @plan, title, content, url)
      end
    end

    # Use callbacks to share common setup or constraints between actions.
    def set_plan
      @plan = Plan.find_by(id: params[:id], organization_id: current_organization_id)
    end

    def set_project
      @project = current_organization.projects.find_by(id: params[:project_id])
    end

    # Only allow a trusted parameter "white list" through.
    def plan_params
      params.require(:plan).permit(:order_number, :level, :name, :content, :started_at, :ended_at, :act_started_at, :act_ended_at, :status, :p_type, :p_priority, :parent_id, :project_id, :user_id, :duty_user_id, :organization_id)
    end
end