var layer, form, current_week_table, current_week_table2;

function initializeTable(elem, url){
  layui.use(function(){
    var tableInstance = layui.table;
    tableInstance.render({
      id: elem,
      height: '400px',
      elem: `#${elem}`,
      url: url, //数据接口
      title: '工作台',
      skin: 'line',
      page: false, //开启分页
      toolbar: false,
      cols: [[
      {field: 'id', title: '编号', width: 80, hide: true},
        {field: 'name', align: 'left', title: '名称', minWidth: 200, templet: function (d) {
          return `<span style='color: #2468f2;' target="_blank" class='click_detail' data-href='${d.url}'>${d.name}</span>`;
        }},
        {field: 'priority', align: 'left', title: '优先级', width: 80, templet: function (d) {
          return `<span class="layui-badge ${d.priority[1]}">${d.priority[0]}</span>`;
        }},
        {field: 'data_type_title', align: 'left', title: '类型', width: 80},
        {field: 'status', align: 'left', title: '状态', width: 75},
        {field: 'project_name', align: 'left', title: '项目名称', minWidth: 180, templet: function (d) {
          return `<a style='color: #2468f2;' target="_blank" lay-href="/admin/projects/${d.project_id}" lay-title="${d.project_name}的项目详情">${d.project_name}</a>`;
        }},
        {field: 'around_time', align: 'left', title: '时间范围', minWidth: 300},
        {fixed: 'right', align: 'left', title: '操作', minWidth: 180, templet: function (d)
          {
            var button = '';
            d.button.forEach(res => {
              if (res.child && res.child.length > 0){
                res.child.forEach(child_res => {
                  button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_detail_${child_res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${child_res.id}">${res.title}${child_res.title}</a>`
                })
              }else{
                button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_${res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${res.id}">${res.title}</a>`
              }
            });
            return button;
          }
        }
      ]]
    });

    if (elem === 'data_modules_index') {
      current_week_table = tableInstance;
    } else {
      current_week_table2 = tableInstance;
    }
  })
}

layui.use(function(){
  layui.table.render({
    id: 'project-table',
    height: '300px',
    elem: `#project-table`,
    url: '/admin/welcomes/projects', //数据接口
    title: '项目列表',
    skin: 'line',
    page: false, //开启分页
    toolbar: false,
    cols: [[
      {field: 'project_name', align: 'left', title: '项目名称', minWidth: 180, templet: function (d) {
        return `<a style='color: #2468f2;' target="_blank" lay-href="/admin/projects/${d.id}" lay-title="${d.name}的项目详情">${d.name}</a>`;
      }},
      {field: 'project_type', align: 'left', title: '项目类型', width: 100},
      {field: 'organization_name', align: 'left', title: '所属组织', width: 150}
    ]]
  });
})


$(document).on('click', '#current_week, #next_week', function(){
  layui.table.resize()
})

$(document).on('click', '.click_detail', function(){
  $.ajax({
    type: 'GET',
    dataType: 'script',
    url: $(this).attr('data-href')
  })
})

// 项目任务
$(document).on('click', '.plans_in_progress, .plans_discontinued, .plans_completed', function(){
  var status = $(this).attr('data-event');
  var id = $(this).attr('data-uuid');
  var project_id = $(this).attr('data-project_id');

  if(status === 'in_progress' || status === 'completed'){
    layer.confirm("确认开始此任务吗？", function(index){
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_plan_status',
        data: {
          id: id,
          status: status
        },success:function(res){
          if (res.status){
            layer.msg(res.message);
            update_count();
          }else{
            layer.msg(res.message);
          }
        },error: function() {
          layer.msg('报错了，请联系管理员');
        }
      })
      layer.close(index);
    })
  }else if(status === 'discontinued'){ // 中止
    layer.prompt({title: '请输入中止原因'}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_plan_status',
        data: {
          id: id,
          status: status,
          reason: value
        },
        success: function(res){
          if (res.status){
            layer.msg(res.message);
            update_count();
          }else{
            layer.msg(res.message);
          }
        }
      })
      // 关闭 prompt
      layer.close(index);
    });
  }

});


$(document).on('click', '.work_orders_confirm_closed, .work_orders_manual_closed, .work_orders_appoint, .work_orders_unacceptance, .work_orders_solved', function(){
  var status = $(this).attr('data-event');
  var id = $(this).attr('data-uuid');
  var project_id = $(this).attr('data-project_id');

  if (status == 'confirm_closed' || status == 'manual_closed'){
    layer.prompt({title: '请输入关闭原因', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          reason: value
        },
        success: function(res){
          if (res.status){
            layer.msg(res.message);
            update_count();
          }else{
            layer.msg(res.message);
          }
        }
      })
      layer.close(index);
    });
  }else if(status == 'appoint') { // 指派
    var obligation_user;
    layer.open({
      type: 1,
      title: '指派责任人',
      area: ['50%', '40%'],
      shade: 0.5,
      maxmin: false,
      offset: 'auto',
      content: `<div class="layui-form layui-padding-3">
                  <div style="width: 100%;" class="obligation_user_id"></div>
                </div>`,
      btn: ['保存', '关闭'],
      yes: function(index){
        $.ajax({
          type: 'POST',
          url: '/admin/welcomes/update_work_order_status',
          data: {
            id: id,
            status: status,
            obligation_user_id: obligation_user.getValue('value')[0]
          },success:function(res){
            if (res.status){
              layer.msg(res.message);
              update_count();
              layer.close(index);
            }else{
              layer.msg(res.message);
            }
          },error: function() {
            layer.msg('报错了，请联系管理员');
          }
        })
      },success: function(layero, index){
        $.ajax({
          url: "/admin/public_api/search_project_users",
          type: 'GET',
          data: {
            project_id: project_id
          },
          success:function(res){
            obligation_user = layui.xmSelect.render({
              el: '.obligation_user_id', // 将jQuery对象转换为DOM元素
              autoRow: true,
              radio: true,
              name: 'obligation_user_id',
              toolbar: { show: true },
              tips: '选择责任人',
              filterable: true,
              layVerify: 'required',
              remoteSearch: false,
              data: res.data
            });
          }
        })

      }
    });
  }else if(status == 'unacceptance'){ // 拒绝
    layer.prompt({title: '请输入驳回原因', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          rejection_reason: value
        },
        success: function(res){
          if (res.status){
            layer.msg(res.message);
            update_count();
          }else{
            layer.msg(res.message);
          }
        }
      })
      layer.close(index);
    });
  }else if(status == 'solved'){ // 解决
    layer.prompt({title: '请输入解决方案', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          solution: value
        },
        success: function(res){
          if (res.status){
            layer.msg(res.message);
            update_count();
          }else{
            layer.msg(res.message);
          }
        }
      })
      layer.close(index);
    });
  }


})

function update_count(){
  if ($('#current_week').hasClass('layui-this')) {
    current_week_table.reload('data_modules_index');
  } else {
    current_week_table2.reload('data_modules_index2');
  }
}