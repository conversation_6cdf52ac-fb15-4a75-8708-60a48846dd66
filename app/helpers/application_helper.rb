module ApplicationHelper
  def crumbs_list(actions, url='/')
    content_tag(:span, class: 'layui-breadcrumb', style: 'padding: 10px') do
      actions.each do |action|
        url = "#{url}#{action}/"
        name = action.to_i > 0 ? action : (I18n.t action, scope: [:actions])
        if actions.last == action
          cite = content_tag(:cite, (name))
          concat content_tag(:a, cite, href: url)
        else
          concat content_tag(:a, (name), href: url)
        end
      end
    end
  end
end
