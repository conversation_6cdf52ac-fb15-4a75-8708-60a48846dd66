# == Schema Information
#
# Table name: permission_actions
#
#  id                                 :bigint           not null, primary key
#  deleted_at(删除时间)               :datetime
#  name(动作名称)                     :string
#  order_number(排序)                 :integer
#  word(别名)                         :string
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  organization_id(组织ID)            :integer
#  permission_controller_id(控制器ID) :integer
#
# Indexes
#
#  index_permission_actions_on_deleted_at                (deleted_at)
#  index_permission_actions_on_organization_id           (organization_id)
#  index_permission_actions_on_permission_controller_id  (permission_controller_id)
#
class PermissionAction < ApplicationRecord
  belongs_to :organization
  belongs_to :permission_controller

  # 验证当前权限动作是否可以被指定组织分配
  def can_be_assigned_by?(organization)
    return true if self.organization_id == organization.id

    # 如果是子企业，检查父企业是否拥有此权限动作
    if organization.parent_id.present?
      organization.parent.permission_actions.exists?(
        word: self.word,
        permission_controller: { word: self.permission_controller.word }
      )
    else
      false
    end
  end

  # 获取可以被指定组织分配的权限动作
  scope :assignable_by, ->(organization) {
    if organization.parent_id.present?
      # 子企业只能看到父企业的权限动作
      joins(:permission_controller).where(
        organization_id: organization.parent_id,
        permission_controllers: { organization_id: organization.parent_id }
      )
    else
      # 顶级企业只能看到自己的权限动作
      where(organization_id: organization.id)
    end
  }
end
