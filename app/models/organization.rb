# == Schema Information
#
# Table name: organizations
#
#  id                                                :bigint           not null, primary key
#  name(企业名称)                                    :string
#  org_type(企业类型: 1: 代理商 2: 方案商 3: 品牌商) :integer
#  created_at                                        :datetime         not null
#  updated_at                                        :datetime         not null
#  parent_id(父级企业ID)                             :integer
#
# Indexes
#
#  index_organizations_on_parent_id  (parent_id)
#
class Organization < ApplicationRecord
  has_many :children, class_name: "Organization", foreign_key: "parent_id", dependent: :destroy
  has_many :users, dependent: :destroy
  has_many :roles, dependent: :destroy
  has_many :permission_controllers, dependent: :destroy
  has_many :permission_actions, dependent: :destroy
  has_many :role_permissions, dependent: :destroy
  has_many :user_roles, dependent: :destroy
  has_many :project_organizations, dependent: :destroy
  has_many :projects, through: :project_organizations

  belongs_to :parent, class_name: "Organization", foreign_key: "parent_id", optional: true

  enum org_type: {
    agent: 1,
    solution_provider: 2,
    brand: 3
  }

  validates :name, presence: true
  validates :org_type, presence: true

  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "deleted_at", "id", "name", "org_type", "parent_id", "updated_at"]
  end

  def generate_admin_auth
    abilities = YAML.load_file("#{Rails.root}/config/admin_auth.yml")
    abilities.each_with_index do |ability, index|
      permission_controller = PermissionController.find_or_create_by(word: ability.keys.first, organization_id: self.id)
      permission_controller.update(name: ability.dig(ability.keys.first), order_number: index + 1)
      ability['children'].each_with_index do |child, action_index|
        permission_action = permission_controller.permission_actions.find_or_create_by(word: child.keys.first, organization_id: self.id)
        permission_action.update(name: child.dig(child.keys.first), order_number: action_index + 1)
      end
    end
  end

  # 获取企业可分配的权限范围（包括继承的权限）
  def available_permission_actions
    if parent_id.present?
      # 子企业只能分配父企业拥有的权限
      parent.permission_actions
    else
      # 顶级企业可以分配自己的所有权限
      permission_actions
    end
  end

  # 获取企业可分配的权限控制器范围
  def available_permission_controllers
    if parent_id.present?
      # 子企业只能分配父企业拥有的权限控制器
      parent.permission_controllers
    else
      # 顶级企业可以分配自己的所有权限控制器
      permission_controllers
    end
  end

  # 检查企业是否有权限分配指定的权限动作
  def can_assign_permission_action?(permission_action_id)
    available_permission_actions.exists?(id: permission_action_id)
  end

  # 检查企业是否有权限分配指定的权限控制器
  def can_assign_permission_controller?(permission_controller_id)
    available_permission_controllers.exists?(id: permission_controller_id)
  end

  # 为子企业生成继承的权限（基于父企业的权限）
  def generate_inherited_permissions
    return unless parent_id.present?

    parent.permission_controllers.each do |parent_controller|
      # 创建或更新权限控制器
      controller = permission_controllers.find_or_create_by(word: parent_controller.word)
      controller.update(
        name: parent_controller.name,
        order_number: parent_controller.order_number
      )

      # 创建或更新权限动作
      parent_controller.permission_actions.each do |parent_action|
        action = controller.permission_actions.find_or_create_by(
          word: parent_action.word,
          organization_id: self.id
        )
        action.update(
          name: parent_action.name,
          order_number: parent_action.order_number
        )
      end
    end
  end

  def project_create_button
    array = [{
      title: '内部方案',
      type: 'p_internal'
    }]
    # if self.parent_id.present?
    #   array << {title: '技术支持案', type: 'p_support'} if OrganizationFlow.find_by(flow_type: 'f_project_support', organization_id: self.parent_id).present?
    #   array << {title: '联合开发案', type: 'p_joint'} if OrganizationFlow.find_by(flow_type: 'f_project_joint', organization_id: self.parent_id).present?
    # end
    array << {title: '技术支持案', type: 'p_support'}
    array << {title: '联合开发案', type: 'p_joint'}
    array
  end
end
