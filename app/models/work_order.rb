# == Schema Information
#
# Table name: work_orders
#
#  id                                             :bigint           not null, primary key
#  aasm_state(状态 ｜ 问题状态)                   :string
#  act_ended_at(实际结束时间)                     :datetime
#  act_started_at(实际开始时间)                   :datetime
#  closed_at(关闭时间)                            :datetime
#  customer_confirmation(客户确认)                :string
#  customer_name(客户名称)                        :string
#  deleted_at(软删除、删除时间)                   :datetime
#  demand_sources(需求来源)                       :integer
#  description(问题描述)                          :string
#  ended_at(结束时间)                             :datetime
#  file(文件)                                     :string
#  founder_email(创建邮箱)                        :string
#  founder_phone(创建电话)                        :string
#  hardware_version(硬件版本号)                   :string
#  is_platform_commonality(是否是平台共性)        :boolean
#  priority(优先级)                               :integer
#  problem_severity(问题严重性)                   :integer
#  product_name(产品名称)                         :string
#  project_progress(项目进度)                     :integer
#  rejection_reason(驳回理由)                     :string
#  repro_steps(重现步骤)                          :text
#  solution(解决方案)                             :string
#  started_at(开始时间)                           :datetime
#  title(标题)                                    :string
#  which_module(所属模块)                         :string
#  work_type(工单类型 ｜ 问题类型)                :integer
#  workable_type                                  :string
#  created_at                                     :datetime         not null
#  updated_at                                     :datetime         not null
#  chip_config_id(芯片平台ID)                     :integer
#  chip_os_software_id(OS系统软件ID, 软件)        :integer
#  chip_os_version_id(OS系统软件版本ID, 软件版本) :integer
#  founder_id(创建人)                             :integer
#  obligation_user_id(责任人ID)                   :integer
#  product_category_id(产品类型ID)                :integer
#  project_id(项目ID)                             :integer
#  receiver_user_id(接收人)                       :integer
#  workable_id(多态)                              :bigint
#
# Indexes
#
#  index_work_orders_on_chip_config_id       (chip_config_id)
#  index_work_orders_on_chip_os_version_id   (chip_os_version_id)
#  index_work_orders_on_obligation_user_id   (obligation_user_id)
#  index_work_orders_on_product_category_id  (product_category_id)
#  index_work_orders_on_project_id           (project_id)
#  index_work_orders_on_receiver_user_id     (receiver_user_id)
#  index_work_orders_on_workable             (workable_type,workable_id)
#
class WorkOrder < ApplicationRecord
  include AasmWorkOrder
  has_paper_trail ignore: [:updated_at]

  acts_as_paranoid
  belongs_to :workable, polymorphic: true, optional: true
  belongs_to :receiver_user, class_name: 'User', foreign_key: :receiver_user_id, optional: true # 评估人员
  belongs_to :obligation_user, class_name: 'User', foreign_key: :obligation_user_id, optional: true # 责任人
  belongs_to :founder, class_name: 'User', foreign_key: :founder_id # 创建人
  belongs_to :project
  belongs_to :chip_os_software, optional: true
  belongs_to :chip_os_version, optional: true
  belongs_to :product_category, optional: true
  belongs_to :chip_config, optional: true
  has_many :approval_flows, as: :flowable, dependent: :destroy
  has_many :work_order_reason_logs, dependent: :destroy
  #工单类型 ｜ 问题类型
  # 需求、 bug
  enum :work_type, {demand: 1, bug: 2}
  #优先级
  # A B C D
  enum :priority, [:A, :B, :C, :D]
  #需求来源
  #物料停产/价格优势/竞争力/体验/认证要求/新产品新物料/旧产品新物料
  enum :demand_sources, {material_stop_production: 1, price_advantage: 2, competitiveness: 3, experience: 4, certification_requirements: 5, new_product_new_material: 6, old_product_new_material: 7}
  #项目进度
  #需求评估Desginin/立项 Kick off/功能开发EVT/系统测试DVT/小批量试产PVT/量产MP产品维护/正常结项/异常结项
  enum :project_progress, {design: 1, kick_off: 2, evt: 3, dvt: 4, pvt: 5, mp_product_maintenance: 6, normal_end: 7, exception_end: 8}
  #问题严重性
  #低 中 高
  enum :problem_severity, {low: 1, medium: 2, high: 3}

  attr_accessor :button_tag, :reason_type_for_log

  after_save :create_reason_log

  validate :validate_start_and_end_time

  def create_reason_log
    change_hash = self.saved_changes
    case
    when change_hash.has_key?(:rejection_reason)
      self.work_order_reason_logs.create(reason: self.rejection_reason, reason_type: "rejection")
    when change_hash.has_key?(:solution)
      self.work_order_reason_logs.create(reason: self.solution, reason_type: "t_solution")
    when change_hash.has_key?(:customer_confirmation)
      p self.reason_type_for_log
      reason_type = self.reason_type_for_log || "confirm_closed"
      self.work_order_reason_logs.create(reason: self.customer_confirmation, reason_type: reason_type)
    end
  end

  def current_approval_flow
    self.approval_flows.find_by(is_effect: true)
  end

  # 优先级颜色标识
  def priority_color
    case self.priority
    when 'A'
      [self.priority_i18n, ""]
    when 'B'
      [self.priority_i18n, "#layui-bg-orange"]
    when 'C'
      [self.priority_i18n, "layui-bg-blue"]
    when 'D'
      [self.priority_i18n, "layui-bg-green"]
    end
  end

  # 获取按钮
  def get_button(current_user)
    button = []
    case self.aasm_state
    when 'not_started'
      button << { title: '编辑', id: 'edit' }
      button << { title: '删除', id: 'deleted' } if current_user.id == self.founder_id # 客户
      button << { title: '指定评估人', id: 'add_receiver_user' } if project.project_power_keys(current_user.id).include?("FAE") # FAE
    when 'evaluating'
      button << { title: '关闭', id: 'confirm_closed' } if current_user.id == self.founder_id # 客户
      button << { title: '指派', id: 'appoint' } if current_user.id == self.receiver_user_id # 评估人
      button << { title: '驳回', id: 'unacceptance' } if current_user.id == self.receiver_user_id # 评估人
    when 'in_progress'
      button << { title: '关闭', id: 'manual_closed' } if current_user.id == self.founder_id # 客户
      button << { title: '解决', id: 'solved' } if current_user.id == self.obligation_user_id # 责任人
    when 'solved'
      button << { title: '已解决', id: 'already_solved' } if current_user.id == self.founder_id # 客户
      button << { title: '未解决', id: 'unsolved' } if current_user.id == self.founder_id # 客户
    when 'close'
      button << { title: '重新打开', id: 'open_again' } if current_user.id == self.founder_id # 客户
    else
      # 默认情况处理
    end
    button.flatten.uniq
  end

  # 用于工作台统一调用方法
  def data_type_title
    return "需求" if self.demand?
    return "Bug" if self.bug?
  end

  def build_change_log
    change_logs = []
    versions = self.versions.reorder(created_at: :desc)

    versions.each do |version|
      change_at = version.created_at.strftime("%Y-%m-%d %H:%M")
      change_by = User.find_by(id: version.whodunnit)&.name || "未知用户"
      change_type = version.event

      if version.event == "create"
        change_logs << {
          change_at: change_at,
          change_by: change_by,
          change_type: change_type,
          changes: "新建"
        }
      elsif version.event == "update"
        changeset = version.changeset.presence || {}
        formatted_changes = format_changeset(changeset)

        unless formatted_changes.empty?
          change_logs << {
            change_at: change_at,
            change_by: change_by,
            change_type: change_type,
            changes: formatted_changes
          }
        end
      elsif version.event == "destroy"
        change_logs << {
          change_at: change_at,
          change_by: change_by,
          change_type: change_type,
          changes: "已删除"
        }
      end
    end

    change_logs
  end

  def format_changeset(changeset)
    changes = []

    # 定义需要格式化为时间的字段
    time_fields = [:started_at, :ended_at, :act_started_at, :act_ended_at, :closed_at]

    # 定义需要翻译的字段及其对应 i18n 前缀
    translatable_fields = {
      work_type: 'enums.work_order.work_type',
      priority: 'enums.work_order.priority',
      demand_sources: 'enums.work_order.demand_sources',
      project_progress: 'enums.work_order.project_progress',
      problem_severity: 'enums.work_order.problem_severity',
      aasm_state: 'workflows.work_order.aasm_state'
    }

    changeset.each do |field, (old_value, new_value)|
      next if ["id", "created_at", "deleted_at"].include?(field.to_s)

      field_sym = field.to_sym

      # 格式化时间字段
      if time_fields.include?(field_sym)
        old_value = Time.parse(old_value).strftime("%F %T") if old_value.present?
        new_value = Time.parse(new_value).strftime("%F %T") if new_value.present?
      end

      if field == 'receiver_user_id' || field == 'obligation_user_id' || field == 'founder_id'
        old_value = User.find(old_value).name if old_value.present?
        new_value = User.find(new_value).name if new_value.present?
      end

      # 国际化枚举字段
      if translatable_fields.key?(field_sym)
        old_value = old_value.presence && I18n.t("#{translatable_fields[field_sym]}.#{old_value}")
        new_value = new_value.presence && I18n.t("#{translatable_fields[field_sym]}.#{new_value}")
      end
      field = I18n.t("activerecord.attributes.work_order.#{field}")
      changes << {
        field: field,
        from: old_value,
        to: new_value
      }
    end

    changes
  end

  def self.ransackable_attributes(auth_object = nil)
    ["aasm_state", "chip_config_id", "chip_os_software_id", "chip_os_version_id", "created_at", "customer_name", "deleted_at", "demand_sources", "description", "ended_at", "file", "founder_email", "founder_id", "founder_phone", "hardware_version", "id", "is_platform_commonality", "obligation_user_id", "priority", "problem_severity", "product_category_id", "product_name", "project_id", "project_progress", "receiver_user_id", "repro_steps", "started_at", "title", "updated_at", "which_module", "work_type", "workable_id", "workable_type"]
  end

  def status_i18n
    I18n.t("workflows.work_order.aasm_state.#{aasm_state}")
  end

  #能看见工单的人
  def self.where_current_user(current_user)
    self.where("receiver_user_id = ? OR obligation_user_id = ? OR founder_id = ?", current_user.id, current_user.id, current_user.id)
  end

  # 查询延期的工单
  def self.where_delay_work_order(project_id, work_type = nil)
    work_orders = where(aasm_state: %w[not_started evaluating in_progress reopen], project_id: project_id).where("ended_at < ?", Time.now)
    work_orders = work_orders.where(work_type: work_type) if work_type.present?
    work_orders
  end

  def is_display_button(current_user)
    arr = []
    case self.aasm_state
    when 'not_started'
      arr += ["edit", "deleted"] if current_user.id == self.founder_id #客户
      arr += [] if current_user.id == self.receiver_user_id #评估人
      arr += [] if current_user.id == self.obligation_user_id #责任人
      project = self.project
      permission_arr = project.project_power_keys(current_user.id)
      arr += ["add_receiver_user"] if permission_arr.include?("FAE") #fae
    when 'evaluating'
      arr += ["closed"] if current_user.id == self.founder_id #客户
      arr += ["appoint", "unacceptance"] if current_user.id == self.receiver_user_id #评估人
      arr += [] if current_user.id == self.obligation_user_id #责任人
    when 'in_progress'
      arr += ["closed"] if current_user.id == self.founder_id #客户
      arr += [] if current_user.id == self.receiver_user_id #评估人
      arr += ["solved"] if current_user.id == self.obligation_user_id #责任人
    when 'solved'
      arr += ["already_solved", "unsolved"] if current_user.id == self.founder_id #客户
      arr += [] if current_user.id == self.receiver_user_id #评估人
      arr += [] if current_user.id == self.obligation_user_id #责任人
    when 'close'
      arr += ["open_again"] if current_user.id == self.founder_id #客户
      arr += [] if current_user.id == self.receiver_user_id #评估人
      arr += [] if current_user.id == self.obligation_user_id #责任人
    else

    end
    # arr << "add_receiver_user"
    # arr << "unacceptance"
    # arr << "appoint"
    # arr << "solved"
    # arr << "already_solved"
    # arr << "unsolved"
    arr
  end

  def validate_start_and_end_time
    if self.started_at.present? && self.ended_at.present?
      errors.add(:error, "开始时间不能大于结束时间") and return if self.started_at > self.ended_at
    end
  end

end
