# == Schema Information
#
# Table name: permission_controllers
#
#  id                      :bigint           not null, primary key
#  deleted_at(删除时间)    :datetime
#  name(控制器名称)        :string
#  order_number(排序)      :integer
#  word(别名)              :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#
# Indexes
#
#  index_permission_controllers_on_deleted_at       (deleted_at)
#  index_permission_controllers_on_organization_id  (organization_id)
#
class PermissionController < ApplicationRecord
  belongs_to :organization
  has_many :permission_actions, dependent: :destroy
end
