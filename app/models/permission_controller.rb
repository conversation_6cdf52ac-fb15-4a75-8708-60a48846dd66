# == Schema Information
#
# Table name: permission_controllers
#
#  id                      :bigint           not null, primary key
#  deleted_at(删除时间)    :datetime
#  name(控制器名称)        :string
#  order_number(排序)      :integer
#  word(别名)              :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#
# Indexes
#
#  index_permission_controllers_on_deleted_at       (deleted_at)
#  index_permission_controllers_on_organization_id  (organization_id)
#
class PermissionController < ApplicationRecord
  belongs_to :organization
  has_many :permission_actions, dependent: :destroy

  # 验证当前权限控制器是否可以被指定组织分配
  def can_be_assigned_by?(organization)
    return true if self.organization_id == organization.id

    # 如果是子企业，检查父企业是否拥有此权限控制器
    if organization.parent_id.present?
      organization.parent.permission_controllers.exists?(word: self.word)
    else
      false
    end
  end

  # 获取可以被指定组织分配的权限控制器
  scope :assignable_by, ->(organization) {
    if organization.parent_id.present?
      # 子企业只能看到父企业的权限控制器
      where(organization_id: organization.parent_id)
    else
      # 顶级企业只能看到自己的权限控制器
      where(organization_id: organization.id)
    end
  }
end
