# == Schema Information
#
# Table name: role_permissions
#
#  id                           :bigint           not null, primary key
#  deleted_at(删除时间)         :datetime
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  organization_id(组织ID)      :integer
#  permission_action_id(权限ID) :integer
#  role_id(角色ID)              :integer
#
# Indexes
#
#  index_role_permissions_on_deleted_at            (deleted_at)
#  index_role_permissions_on_organization_id       (organization_id)
#  index_role_permissions_on_permission_action_id  (permission_action_id)
#  index_role_permissions_on_role_id               (role_id)
#
class RolePermission < ApplicationRecord
  belongs_to :role
  belongs_to :permission_action
  belongs_to :organization

  # 验证权限分配的合法性
  validate :validate_permission_assignment

  private

  def validate_permission_assignment
    return unless role&.organization && permission_action

    # 验证角色的组织是否有权限分配此权限动作
    unless role.organization.can_assign_permission_action?(permission_action.id)
      errors.add(:permission_action, "该组织无权限分配此权限动作")
    end

    # 验证组织ID一致性
    if organization_id != role.organization_id
      errors.add(:organization_id, "组织ID必须与角色的组织ID一致")
    end
  end
end
