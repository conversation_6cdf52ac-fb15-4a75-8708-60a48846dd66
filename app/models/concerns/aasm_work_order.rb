module AasmWorkOrder
  extend ActiveSupport::Concern

  included  do
    include AASM

    ##
    # @class Aasm_cash_audit
    # @Description:
    # (1) not_started 新建：
    # (2) evaluating 评估中：
    # (3) in_progress 进行中：
    # (4) solved 已解决：
    # (6) close 关闭：
    # @params:
    # @return: nil
    aasm do
      state :not_started, :initial => true
      state :evaluating, :in_progress, :solved, :close

      # 新建 -> 评估中
      event :evaluate do
        transitions :from => :not_started, :to => :evaluating
      end

      # 评估中 -> 进行中
      event :in_progress do
        transitions :from => [:evaluating, :solved], :to => :in_progress, :after => :update_act_started_at
      end

      # 进行中 -> 已解决
      event :solved do
        transitions :from => :in_progress, :to => :solved, :after => :update_act_ended_at
      end

      # 再打开
      # 关闭 -> 评估中
      event :reopened do
        transitions :from => [:close], :to => :evaluating
      end

      # (评估中 | 进行中) -> 关闭
      event :closed do
        transitions :from => [:evaluating, :in_progress, :solved], :to => :close, :after => :update_closed_at
      end

    end
  end

  def update_act_started_at
    self.update!(act_started_at: Time.now)
  end

  def update_closed_at
    self.update!(closed_at: Time.now)
  end

  def update_act_ended_at
    self.update!(act_ended_at: Time.now)
  end

end
