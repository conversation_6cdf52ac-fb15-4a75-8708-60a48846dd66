layer.open({
  id: 'show',
	type: 1,
	title: '详情',
	area: ['80%', '90%'],
	shade: 0.5,
	maxmin: true,
	offset: 'auto',
	content: `
    <div class='flex' style="height: 100%;">
      <div class="layui-panel" style='width: 45%;'>
        <div style="padding: 32px;">
          <h4>详情</h4>
          <table class="layui-table" style='margin: auto;' lay-skin="nob">
            <colgroup>
              <col width="120">
              <col>
            </colgroup>

            <tbody>
              <tr>
                <th>名称:</th>
                <td><%= @plan.name %></td>
              </tr>

              <tr>
                <th>内容:</th>
                <td><%= @plan.content %></td>
              </tr>

              <tr>
                <th>状态:</th>
                <td><%= @plan.status_i18n %></td>
              </tr>
              <tr>
                <th>类型:</th>
                <td><%= @plan.p_type_i18n %></td>
              </tr>

              <tr>
                <th>优先级:</th>
                <td><%= @plan.p_priority_i18n %></td>
              </tr>

              <tr>
                <th>父级任务:</th>
                <td><%= @plan.parent_plan&.name %></td>
              </tr>

              <tr>
                <th>所属项目:</th>
                <td><%= @plan.project.name %></td>
              </tr>

              <tr>
                <th>责任人:</th>
                <td><%= @plan.duty_user&.name %></td>
              </tr>

              <tr>
                <th>计划开始时间:</th>
                <td><%= @plan.started_at.strftime('%F %T') %></td>
              </tr>

              <tr>
                <th>计划结束时间:</th>
                <td><%= @plan.ended_at.strftime('%F %T') %></td>
              </tr>

              <tr>
                <th>实际开始时间:</th>
                <td><%= @plan.act_started_at&.strftime('%F %T') %></td>
              </tr>

              <tr>
                <th>实际结束时间:</th>
                <td><%= @plan.act_ended_at&.strftime('%F %T') %></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="layui-panel" style='width: 55%;'>
        <div style="padding: 32px; height: calc(100% - 70px);">
          <h4>变动记录</h4>

          <div class="layui-timeline" style='overflow: auto; height: calc(100% - 32px);'>
            <% @plan.build_change_log.each do |log| %>
              <div class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis"></i>
                <div class="layui-timeline-content layui-text">
                  <h5 class="layui-timeline-title"><%= log[:change_at] %></h5>
                  <% if log[:change_type] == 'create' %>
                    <p>创建任务</p>
                  <% else %>
                    <% log[:changes].each do |change| %>
                      <p>
                        <span style='color: #1e9fff'><%= change[:field] %></span><br>
                        由 “<span style='color: #ffb800'><%= change[:from] %></span>”
                        变动为 “<span style='color: #16b777'><%= change[:to] %></span>”
                      </p>
                    <% end %>
                  <% end %>

                </div>
              </div>
            <% end %>

          </div>
        </div>
      </div>
    </div>
  `
});
