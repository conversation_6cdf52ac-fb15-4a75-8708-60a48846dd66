<%= simple_form_for([:admin, @plan], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>
    <%= f.input :name, label: '名称', input_html: {'lay-verify': "required"} %>
    <%= f.input :content, as: :text, label: '内容', input_html: {'class': "layui-textarea"} %>
    <div class="project-form">
      <%= f.input :started_at, label: '开始时间', input_html: {'lay-verify': "required", class: 'started_at', autocomplete: 'off', value: f.object.started_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择计划开始时间", readonly: true, unit: "&#xe637;"%>
      <%= f.input :ended_at, label: '结束时间', input_html: {'lay-verify': "required", class: 'ended_at', autocomplete: 'off', value: f.object.ended_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择计划结束时间", readonly: true, unit: "&#xe637;"%>
    </div>

    <div class="project-form">
      <%= f.input :status, label: '状态', input_html: {'lay-verify': "required"} %>
      <%= f.input :p_priority, label: '优先级', input_html: {'lay-verify': "required"} %>
    </div>

    <div class="project-form">
      <%= f.input :p_type, label: '类型', input_html: {'lay-verify': "required"} %>
      <%= f.input :duty_user_id, label: '责任人', collection: @users, input_html: {'lay-verify': "required"}%>
    </div>

    <%= f.input :parent_id, label: '父级计划', collection: @parents %>
    <%= f.input :project_id, input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <%= f.input :user_id, input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <%= f.input :organization_id, label: '组织ID', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
    <%= f.input :sort_ids, wrapper_html: {style: 'display:none'}%>
  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      var laydate = layui.laydate;

      laydate.render({
        elem: '.started_at',
        type: 'datetime',
        fullPanel: true,
        min: `<%= @min_started_at %>`,
        max: `<%= @max_ended_at %>`
      });

      laydate.render({
        elem: '.ended_at',
        type: 'datetime',
        fullPanel: true,
        value: `<%= Time.now.end_of_day.strftime("%F %T") %>`,
        isInitValue: false,
        min: `<%= @min_started_at %>`,
        max: `<%= @max_ended_at %>`
      });

      form.render();
    });
  });
</script>