<!--工单局部表单：需求表单-->
<%= simple_form_for([:admin, @work_order], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.error_notification %>

  <% if @work_order.new_record? %>
    <%= f.input :project_id, collection: Project.all, selected: @project.id, wrapper_html: {style: 'display:none'} %>
    <%= f.input :work_type, selected: "demand", wrapper_html: {style: 'display:none'} %>
  <% end %>
  <%= f.input :title %>
  <%= f.input :description %>
  <%#= f.input :problem_severity %>
  <%= f.input :priority %>
  <%= f.input :demand_sources %>
  <%#= f.input :is_platform_commonality %>
  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">芯片平台</label>
      <div class="layui-input-block flex-item">
        <div id="select_chip_config_id" style="width: 99%;" data-value="<%= @chip_config_array.to_json %>"></div>
      </div>
    </div>
  </div>
  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">OS系统</label>
      <div class="layui-input-block flex-item">
        <div id="select_chip_os_software_id" style="width: 99%;" data-value="<%= @chip_os_software_array.to_json %>"></div>
      </div>
    </div>
  </div>
  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">版本号</label>
      <div class="layui-input-block flex-item">
        <div id="select_chip_os_version_id" style="width: 99%;" data-value="<%= @chip_os_version_array.to_json %>"></div>
      </div>
    </div>
  </div>
  <%#= f.input :hardware_version %>
  <%= f.input :product_category_id, collection: @product_category_array %>
  <%= f.input :product_name %>
  <%= f.input :which_module %>
  <%= f.input :customer_name %>
  <%= f.input :project_progress %>
  <%#= f.input :repro_steps %>
  <%= f.input :started_at, input_html: {'lay-verify': "required", class: 'started_at', autocomplete: 'off', value: f.object.started_at&.strftime("%Y-%m-%d %H:%M:%S") || Time.now.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择期望结束时间", readonly: true, unit: "&#xe637;" %>
  <%= f.input :ended_at, input_html: {'lay-verify': "required", class: 'ended_at', autocomplete: 'off', value: f.object.ended_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择期望结束时间", readonly: true, unit: "&#xe637;" %>
  <%#= f.input :founder_email %>
  <%#= f.input :founder_phone %>
  <!--  如果是在打开状态-->
  <% if @event_tag == "open_again" %>
    <%= f.input :receiver_user_id, wrapper_html: {style: 'display:none'} %>
    <%= f.input :obligation_user_id, input_html: {value: nil}, wrapper_html: {style: 'display:none'} %>
  <% end %>
  <%= f.input :file, label: '附件', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
  <div class="layui-form string required work_order_file">
    <div class="layui-form-item">
      <label class="layui-form-label string required">附件</label>
      <div class="layui-input-block flex-item layui-input-wrap">
        <button type="button" class="layui-btn upload-work_order_file" lay-options="{accept: 'file'}">
          <i class="layui-icon layui-icon-upload"></i>
          上传文件
        </button>
        <div class="layui-inline layui-word-aux file_current">
          <% if @work_order.persisted? %>
            <span ><%= @work_order.file.to_filename %><i class='layui-icon layui-icon-clear clear_file' style='color: #ff5722;'></i></span>
          <% end %>
          <a class="down_template" href="javascript:void(0)">下载模板</a>
        </div>
      </div>
    </div>
  </div>


  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
        form = layui.form;
      var upload = layui.upload;
      var laydate = layui.laydate;
      laydate.render({
        elem: '.ended_at',
        type: 'datetime',
        fullPanel: true
      });
      laydate.render({
        elem: '.started_at',
        type: 'datetime',
        fullPanel: true
      });

      upload.render({
        elem: '.upload-work_order_file', // 绑定多个元素
        url: '/upload_anyfile', // 此处配置你自己的上传接口即可
        accept: 'file',
        size: 500000,
        multiple: true,
        done: function(res){
          console.log(res)
          $('.down_template').hide();
          $('#work_order_file').val(res.link);
          var file_html = `<span >${res.file_name}<i class='layui-icon layui-icon-clear clear_file' style='color: #ff5722;'></i></span>`
          $('.file_current').html('');
          $('.file_current').append(file_html)
        },
        error: function(){ // 上传失败的回调
          layer.msg('文件上传失败', {icon: 2});
        }
      });

      form.render();
    });
  });

  $(document).on('click', '.clear_file', function(){
    $(this).parent().remove();
    $('#work_order_file').val('');
    $('.down_template').show();
  });


  $(function () {
    // 初始化 xmSelect 渲染器
    function renderXmSelect(el, name, tips, data, onCallback) {
      layui.xmSelect.render({
        el: el,
        name: name,
        autoRow: true,
        radio: true,
        toolbar: { show: true },
        tips: tips,
        filterable: true,
        remoteSearch: false,
        data: data,
        on: onCallback
      });
    }

    // 渲染芯片配置选择器
    renderXmSelect(
      '#select_chip_config_id',
      'work_order[chip_config_id]',
      '请选择芯片平台',
      JSON.parse($('#select_chip_config_id').attr('data-value')),
      function (data) {
        const selected = data.arr;
        if (selected.length > 0) {
          selectChipOsSoftware(selected[0].value);
        } else {
          selectChipOsSoftware('');
        }
      }
    );

    // 渲染 OS 软件选择器
    function selectChipOsSoftware(chipConfigId) {
      console.log("进入-----------OS");
      if (chipConfigId) {
        fetchData('/admin/public_api/get_chip_sofwares', { chip_config_id: chipConfigId }, function (res) {
          renderXmSelect(
            '#select_chip_os_software_id',
            'work_order[chip_os_software_id]',
            '请选择版本号',
            res.data,
            function (data) {
              const selected = data.arr;
              selectChipOsVersion(selected.length > 0 ? selected[0].value : '');
            }
          );
        });
      } else {
        renderXmSelect(
          '#select_chip_os_software_id',
          'work_order[chip_os_software_id]',
          '请选择版本号',
          JSON.parse($('#select_chip_os_software_id').attr('data-value')),
          function (data) {
            const selected = data.arr;
            selectChipOsVersion(selected.length > 0 ? selected[0].value : '');
          }
        );
      }
    }

    // 渲染 OS 版本号选择器
    function selectChipOsVersion(chipOsSoftwareId) {
      console.log("进入-----------版本号");
      if (chipOsSoftwareId) {
        fetchData('/admin/public_api/get_chip_versions', { chip_os_software_id: chipOsSoftwareId }, function (res) {
          renderXmSelect(
            '#select_chip_os_version_id',
            'work_order[chip_os_version_id]',
            '请选择版本号',
            res.data,
            null // 不需要额外回调
          );
        });
      } else {
        renderXmSelect(
          '#select_chip_os_version_id',
          'work_order[chip_os_version_id]',
          '请选择版本号',
          JSON.parse($('#select_chip_os_version_id').attr('data-value')),
          null // 不需要额外回调
        );
      }
    }

    // 封装 AJAX 数据获取逻辑
    function fetchData(url, params, callback) {
      $.ajax({
        url: url,
        type: 'GET',
        data: params,
        success: function (res) {
          console.log(res);
          callback(res);
        },
        error: function (err) {
          console.error('数据加载失败:', err);
        }
      });
    }

    // 初始化清空状态
    selectChipOsSoftware('');
    selectChipOsVersion('');
  });
</script>