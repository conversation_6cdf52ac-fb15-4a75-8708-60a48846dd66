<div class="layui-card">
  <div class="layui-card-body">
    <%= render 'admin/projects/project_tab' %>
    <table id="table_work_orders" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-border-orange layui-btn-primary" lay-event="show"><i class="layui-icon layui-icon-eye"></i></a>
  {{# layui.each(d.display_button, function(index, item){ }}
  {{#  if(item == "edit" ){ }} <!--编辑-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit"> <i class="layui-icon layui-icon-edit"></i></a>
  {{#  } }}
  {{#  if(item == "delete" ){ }} <!--删除-->
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
  {{#  } }}
  {{#  if(item == "solved" ){ }} <!--解决-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="solved">解决</a>
  {{#  } }}
  {{#  if(item == "open_again" ){ }} <!--再打开-->
  <a class="layui-btn layui-btn-xs layui-border-orange layui-btn-primary" lay-event="open_again">再打开</a>
  {{#  } }}
  {{#  if( item == "closed" ){ }} <!--关闭-->
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="closed">关闭</a>
  {{#  } }}
  {{#  if(item == "appoint" ){ }} <!--指定责任人-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="appoint">指定责任人</a>
  {{#  } }}
  {{#  if(item == "add_receiver_user" ){ }} <!--指定评估人员-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="add_receiver_user">指定评估人员</a>
  {{#  } }}
  {{#  if(item == "unacceptance" ){ }} <!--不受理-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="unacceptance">不受理</a>
  {{#  } }}
  {{#  if(item == "already_solved" ){ }} <!--已解决-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="closed">已解决</a>
  {{#  } }}
  {{#  if(item == "unsolved" ){ }} <!--未解决-->
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="unsolved">未解决</a>
  {{#  } }}

  {{# }) }}
</script>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
</script>

<script>
  var table;
  layui.use(function(){
    table = layui.table
    var layer = layui.layer //弹层
      ,laypage = layui.laypage //分页
      ,page = 1//页码全局变量
      ,limit = 10;//分页大小全局变量

    table.render({
      id: 'listPage',
      elem: '#table_work_orders',
      url: `/admin/work_orders?project_id=<%= @project.id %>&q%5Bproject_id_eq%5D=<%= @project.id %>&q%5Bwork_type_eq%5D=2`, //数据接口
      title: '列表',
      skin: 'line',
      page: true, //开启分页
      limit: limit,
      toolbar: '#myBar',
      cols: [[
        {field: 'project_name', align:'left', title: '项目名称', minWidth: 100},
        {field: 'work_type', align:'left', title: '工单类型', minWidth: 100},
        {field: 'title', align:'left', title: '标题', minWidth: 100},
        // {field: 'description', align:'left', title: '描述', minWidth: 100},
        // {field: 'hardware_version', align:'left', title: '硬件版本', minWidth: 100},
        // {field: 'problem_severity', align:'left', title: '问题严重程度', minWidth: 100},
        {field: 'priority', align:'left', title: '优先级', minWidth: 100},
        // {field: 'demand_sources', align:'left', title: '需求来源', minWidth: 100},
        // {field: 'is_platform_commonality', align:'left', title: '是否是平台共性', minWidth: 100},
        {field: 'chip_config_id', align:'left', title: '芯片平台ID', minWidth: 100},
        {field: 'chip_os_software_id', align:'left', title: 'OS系统软件ID', minWidth: 100},
        {field: 'chip_os_version_id', align:'left', title: 'OS系统软件版本ID', minWidth: 100},
        {field: 'product_category_id', align:'left', title: '产品类型ID', minWidth: 100},
        {field: 'product_name', align:'left', title: '产品名称', minWidth: 100},
        {field: 'which_module', align:'left', title: '所属模块', minWidth: 100},
        // {field: 'customer_name', align:'left', title: '客户名称', minWidth: 100},
        // {field: 'project_progress', align:'left', title: '项目进度', minWidth: 100},
        // {field: 'repro_steps', align:'left', title: '重现步骤', minWidth: 100},
        // {field: 'file', align:'left', title: '附件', minWidth: 100},
        {field: 'started_at', align:'left', title: '开始时间', minWidth: 100},
        {field: 'ended_at', align:'left', title: '结束时间', minWidth: 100},
        {field: 'founder_id', align:'left', title: '创建人', minWidth: 100},
        {field: 'receiver_user_id', align:'left', title: '评估人', minWidth: 100},
        {field: 'obligation_user_id', align:'left', title: '责任人', minWidth: 100},
        // {field: 'founder_email', align:'left', title: '创建人邮件', minWidth: 100},
        // {field: 'founder_phone', align:'left', title: '创建人手机号', minWidth: 100},
        {field: 'aasm_state', align:'left', title: '状态', minWidth: 100},
        // {field: 'workable', align:'left', title: '多态', minWidth: 100},
        {fixed: 'right', title: '', minWidth: 300, align: 'left', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
    table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
        ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit?project_id=<%= @project.id %>&tag=work_bug`,
          data: {
          }
        })
      }else if (layEvent === 'open_again'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit`,
          data: {
            event_tag: "open_again",
            project_id: `<%= @project.id %>`,
            tag: "work_bug"
          }
        })
      }else if (layEvent === 'show'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}`,
          data: {
          }
        })
      }else if (layEvent === 'closed'){
        $.ajax({
          type: 'POST',
          url: `/admin/work_orders/${data.id}/change_state`,
          data: {
            tag: "closed"
          }
        })
      }else if (layEvent === 'unsolved'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit`,
          data: {
            tag: "unsolved",
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'appoint'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit`,
          data: {
            tag: "appoint",
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'unacceptance'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit`,
          data: {
            tag: "unacceptance",
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'add_receiver_user'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit`,
          data: {
            tag: "add_receiver_user",
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'solved'){
        $.ajax({
          type: 'GET',
          url: `/admin/work_orders/${data.id}/edit`,
          data: {
            tag: "solved",
            project_id: `<%= @project.id %>`
          }
        })
      }else if (layEvent === 'deleted'){

        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/work_orders/${data.id}`,
            data: {
            }
          })
        })
      }

    });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
        ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/work_orders/new?project_id=<%= @project.id %>&tag=work_bug`,
            data: {
            }
          })
          break;
      };
    });

  })
</script>