<div class='flex' style="height: 100%;">
  <div class="layui-panel" style='width: 60%;'>
    <div style="padding: 32px;">
      <h4>详情</h4>
      <table class="layui-table" lay-skin="nob">
        <colgroup>
          <col width="120">
          <col>
          <col width="120">
          <col>
        </colgroup>

        <tbody>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:title) %>:
            </th>
            <td>
              <%= @work_order.title %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:aasm_state) %>:
            </th>
            <td>
              <%= t("workflows.work_order.aasm_state.#{@work_order.aasm_state}") %>
            </td>

          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:description) %>:
            </th>
            <td>
              <%= @work_order.description %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:customer_name) %>:
            </th>
            <td>
              <%= @work_order.customer_name %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:priority) %>:
            </th>
            <td>
              <%= @work_order.priority_i18n %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:product_name) %>:
            </th>
            <td>
              <%= @work_order.product_name %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:hardware_version) %>:
            </th>
            <td>
              <%= @work_order.hardware_version %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:file) %>:
            </th>
            <td>
              <%= @work_order.file.to_filename %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:receiver_user_id) %>:
            </th>
            <td>
              <%= @work_order.receiver_user_id %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:which_module) %>:
            </th>
            <td>
              <%= @work_order.which_module %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:problem_severity) %>:
            </th>
            <td>
              <%= @work_order.problem_severity_i18n %>
            </td>

            <th>平台共性:</th>
            <td>
              <%= @work_order.is_platform_commonality ?
                t("workflows.work_order.is_platform_commonality.#{@work_order.is_platform_commonality}") : nil %>
            </td>
          </tr>

          <tr>
            <th>芯片平台:</th>
            <td>
              <%= @work_order.chip_config&.name %>
            </td>
          </tr>

          <tr>
            <th>OS软件:</th>
            <td>
              <%= @work_order.chip_os_software&.name %>
            </td>

            <th>软件版本:</th>
            <td>
              <%= @work_order.chip_os_version&.version %>
            </td>
          </tr>
          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:product_category_id) %>:
            </th>
            <td>
              <%= @work_order.product_category&.name %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:demand_sources) %>:
            </th>
            <td>
              <%= @work_order.demand_sources_i18n %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:repro_steps) %>:
            </th>
            <td>
              <%= @work_order.repro_steps %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:started_at) %>:
            </th>
            <td>
              <%= @work_order.started_at&.strftime("%Y-%m-%d %H:%M:%S") %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:ended_at) %>:
            </th>
            <td>
              <%= @work_order.ended_at&.strftime("%Y-%m-%d %H:%M:%S") %>
            </td>

            <th>创建人:</th>
            <td>
              <%= @work_order.founder.name %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:founder_phone) %>:
            </th>
            <td>
              <%= @work_order.founder_phone %>
            </td>

            <th>
              <%= WorkOrder.human_attribute_name(:founder_email) %>:
            </th>
            <td>
              <%= @work_order.founder_email %>
            </td>
          </tr>

          <tr>
            <th>
              <%= WorkOrder.human_attribute_name(:project_progress) %>:
            </th>
            <td>
              <%= @work_order.project_progress_i18n %>
            </td>
          </tr>

        </tbody>
      </table>
    </div>
  </div>

  <div cl ass="layui-panel" style='width: 40%;'>
    <div style="padding: 32px; height: calc(100% - 70px);">
      <h4>变动记录</h4>

      <div class="layui-timeline" style='overflow: auto; height: calc(100% - 32px);'>
        <% @work_order.build_change_log.each do |log| %>
          <div class="layui-timeline-item">
            <i class="layui-icon layui-timeline-axis"></i>
            <div class="layui-timeline-content layui-text">
              <h5 class="layui-timeline-title">
                <%= log[:change_at] %>
              </h5>
              <% if log[:change_type]=='create' %>
                <p>创建任务</p>
                <% else %>
                  <% log[:changes].each do |change| %>
                    <p>
                      <span style='color: #1e9fff'>
                        <%= change[:field] %>
                      </span><br>
                      由 “<span style='color: #ffb800'>
                        <%= change[:from] %>
                      </span>”
                      变动为 “<span style='color: #16b777'>
                        <%= change[:to] %>
                      </span>”
                    </p>
                    <% end %>
                      <% end %>

            </div>
          </div>
          <% end %>

      </div>
    </div>
  </div>
</div>