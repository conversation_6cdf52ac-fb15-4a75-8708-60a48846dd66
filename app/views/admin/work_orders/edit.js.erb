<% case @tag %>
<% when "work_bug" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'bug_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% when "work_demand" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'demand_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% when "appoint" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'appoint_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% when "add_receiver_user" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'receiver_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% when "unacceptance" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'unacceptance_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% when "solved" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'solved_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% when "unsolved" %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '编辑<%= WorkOrder.model_name.human %>',
		area: ['50%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'unsolved_form') %>",
		btn: ['保存', '关闭'],
		yes: function(){
			$('.save_btn').click();
		},
		success: function(layero, index){
		}
	});
<% else %>
	<% if @event_tag == "open_again" %>
		<% case @work_order.work_type %>
		<% when "demand" %>
			layer.open({
				id: 'edit_open',
				type: 1,
				title: '编辑<%= WorkOrder.model_name.human %>',
				area: ['50%', '85%'],
				shade: 0.5,
				maxmin: true,
				offset: 'auto',
				content: "<%= escape_javascript(render 'demand_form') %>",
				btn: ['保存', '关闭'],
				yes: function(){
					$('.save_btn').click();
				},
				success: function(layero, index){
				}
			});
		<% when "bug" %>
			layer.open({
				id: 'edit_open',
				type: 1,
				title: '编辑<%= WorkOrder.model_name.human %>',
				area: ['50%', '85%'],
				shade: 0.5,
				maxmin: true,
				offset: 'auto',
				content: "<%= escape_javascript(render 'bug_form') %>",
				btn: ['保存', '关闭'],
				yes: function(){
					$('.save_btn').click();
				},
				success: function(layero, index){
				}
			});
		<% end %>
	<% else %>
		layer.open({
			id: 'edit_open',
			type: 1,
			title: '编辑<%= WorkOrder.model_name.human %>',
			area: ['50%', '85%'],
			shade: 0.5,
			maxmin: true,
			offset: 'auto',
			content: "<%= escape_javascript(render 'form') %>",
			btn: ['保存', '关闭'],
			yes: function(){
				$('.save_btn').click();
			},
			success: function(layero, index){
			}
		});
	<% end %>

<% end %>

