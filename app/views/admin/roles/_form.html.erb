<%= simple_form_for([:admin, @role], remote: true, html: {class: 'layui-form'}, wrapper: :seven_form_line) do |f| %>
  <%= f.error_notification %>

  <%= f.input :name %>
  <%= f.input :description %>
  <table class="layui-table mt-2">
    <thead class="table-light">
      <tr>
        <th>模块</th>
        <th>权限点</th>
      </tr>
    </thead>
    <tbody>
      <% current_user.organization.available_permission_controllers.each do |permission_controller| %>
        <tr>
          <td>
            <%= permission_controller.name %>
          </td>
          <td>
            <% permission_controller.permission_actions.each do |permission_action| %>
              <% if current_user.organization.can_assign_permission_action?(permission_action.id) %>
                <%= f.check_box "permission_action_ids", {class: "auth-checkbox", name: "role[permission_action_ids][]", checked: @role.permission_actions.include?(permission_action)}, permission_action.id, nil %>
                <%= f.label permission_action.name, class: "auth-label" %>
              <% end %>
            <% end %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });
</script>