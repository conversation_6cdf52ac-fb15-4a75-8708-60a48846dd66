layer.open({
  id: 'pageList',
	type: 1,
	title: '审核列表',
	area: ['70%', '85%'],
	shade: 0.5,
	maxmin: true,
	offset: 'auto',
	content: `
    <div class="layui-card">
      <div class="layui-card-body">
        <table id="table_flows" lay-filter="admin-table"></table>
      </div>
    </div>

    <script type="text/html" id="barDemo">
      {{# if(d.status == '进行中' ){ }}
        <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="approved"> <i class="layui-icon layui-icon-edit"></i></a>
        <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="rejected"> <i class="layui-icon layui-icon-delete"></i> </a>
      {{#  } }}
    </script>

  `,
  success: function(layero, index){
    var table = layui.table;
    table.render({
    id: 'listPage',
    elem: '#table_flows',
    url: '/admin/welcomes/approval_step_users_list',
    skin: 'line',
    page: true,
    cols: [
        [
          {field: 'name', title: '名称', minWidth: 120},
          {field: 'source_type', align: 'left', title: '来源', minWidth: 200, templet: function (d) {
            return `${d.source_type}(<a style='color: #2468f2;' target="_blank" lay-href="/admin/projects/${d.source_id}" lay-title="${d.source_title}的项目详情">${d.source_title}</a>)`;
          }},
          {field: 'status', title: '状态', width: 120},
          {fixed: 'right', align: 'left', title: '操作', minWidth: 300, templet: function (d)
          {
            var button = ''
            if (d.status == "进行中"){
              button += `
                <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="approved">通过</a>
                <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="rejected">驳回</a>
              `;
            }
            return button;
          }
        }
        ]
      ]
    })

    table.on('tool(admin-table)', function(obj){
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'approved' || layEvent === 'rejected'){ //审核通过
        layer.prompt({title: '请输入审核意见', formType: 2}, function(value, index, elem){
          if(value === '') return elem.focus();
          $.ajax({
            type: 'POST',
            url: `/admin/welcomes/update_approval_step_user`,
            data: {
              id: data.id,
              review_comment: value,
              status: layEvent
            },success:function(res){
              if (res.status){
                layer.close(index);
                table.reload('listPage')
                layer.msg('操作成功', {icon: 1})
              }else{
                layer.msg("操作失败, 请联系管理员", {icon: 2})
              }
            }
          })

        });
      }
	  });
	}
});
