<%= simple_form_for([:admin, @project_user], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form'}) do |f| %>
  <%= f.input :project_id, label: '项目ID', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display: none'} %>
  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">成员</label>
      <div class="layui-input-block flex-item">
        <div id="select_role" style="width: 100%; margin-right: 10px;" data-value="<%= @select_data.to_json %>"></div>
      </div>
    </div>
  </div>

  <div class="layui-form-item">
    <label class="layui-form-label">角色</label>
    <div class="layui-input-block">
      <% ProjectRoleConfig.array_list(current_organization_id).each do |list| %>
        <input type="checkbox" name="project_role_config_id[<%= list.last %>]" title="<%= list.first %>">
      <% end %>
    </div>
  </div>

  <div class="flex" style='display: none'>
    <%= f.submit '保存', data: { disable_with: "保存中..." }, class: 'layui-btn save_btn' %>
    <%= link_to '取消', url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>
<script>

  var layer, layuiTable, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'table', 'form'], function () {
        layer = layui.layer,
        layuiTable = layui.table;
        form = layui.form;
        form.render();
    });
  });
  $('.save_btn').attr('lay-submit', '');

  var select_role;
  layui.use(['xmSelect'], function () {
    var xmSelect = layui.xmSelect
    //渲染数据
    select_role = xmSelect.render({
      el: '#select_role',
      name: 'user_ids',
      autoRow: true,
      toolbar: { show: true },
      tips: '请搜索选择添加成员',
      filterable: true,
      remoteSearch: false,
      data: JSON.parse($('#select_role').attr('data-value'))
    })
    select_role.getValue();
  })

  $(document).on('click', '.search_click', function(){
    var array = []
    select_role.getValue().forEach(function(res){
      array.push(res.value)
    })
    $('#q_id').val(array)
    $('#search_form_q').submit();
  })

</script>