
<%= simple_form_for '', url: update_user_role_admin_project_users_path, method: :post, remote: true, wrapper: :seven_form_line, html:{novalidate: 'novalidate', class: 'layui-form' } do |f| %>
  <%= f.input :project_id, label: '项目ID', input_html: {'lay-verify': "required", value: @project.id}, wrapper_html: {style: 'display: none'} %>
  <%= f.input :user_id, label: '用户ID', input_html: {'lay-verify': "required", value: @user.id}, wrapper_html: {style: 'display: none'} %>

  <div class="layui-form-item">
    <label class="layui-form-label">角色</label>
    <div class="layui-input-block">
      <% ProjectRoleConfig.array_list(current_organization_id).each do |list| %>
        <input type="checkbox" name="project_role_config_id[<%= list.last %>]" title="<%= list.first %>" <%= @check_list.include?(list.last) ? 'checked' : ''%>>
      <% end %>
    </div>
  </div>

  <div class="flex" style='display: none'>
    <%= f.submit '保存', data: { disable_with: "保存中..." }, class: 'layui-btn save_btn' %>
    <%= link_to '取消', url_for(:back), class: 'layui-btn layui-btn-normal' %>
</div>
<% end %>
<script>

var layer, layuiTable, form;//保存layui模块以便全局使用
$(function(){
  //加载&&初始化layui模块
  layui.use(['layer', 'table', 'form'], function () {
      layer = layui.layer,
      layuiTable = layui.table;
      form = layui.form;
      form.render();
  });
});
$('.save_btn').attr('lay-submit', '');

</script>