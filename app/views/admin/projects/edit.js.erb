<% if @project.s_store? || @project.rejected? %>
	layer.open({
		id: 'edit_open',
		type: 1,
		title: '新增<%= Project.model_name.human %>',
		area: ['70%', '85%'],
		shade: 0.5,
		maxmin: true,
		offset: 'auto',
		content: "<%= escape_javascript(render 'form') %>",
		btn: ['发起立项', '暂存'],
		yes: function(){
			$('#project_commit_status').val('applying');
			$('.save_btn').click();
		},
		btn2: function(){
			$('#project_commit_status').val('s_store');
			$('.save_btn').click();
			return false;
		}
	});

<% else %>
	layer.open({
		id: 'edit_open',
		type: 1 //此处以iframe举例
		,title: '编辑<%= Project.model_name.human %>'
		,area: ['50%', '85%']
		,shade: 0.5
		,maxmin: true
		,offset: 'auto'
		,content: "<%= escape_javascript(render 'form') %>"
		,btn: ['保存', '关闭'] //只是为了演示
		,yes: function(){
			$('.save_btn').click();
		}
	});
<% end %>