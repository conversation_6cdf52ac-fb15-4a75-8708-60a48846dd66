layer.open({
  id: 'new_open',
	type: 1,
	title: '新增<%= Project.model_name.human %>',
	area: ['70%', '85%'],
	shade: 0.5,
	maxmin: true,
	offset: 'auto',
	content: "<%= escape_javascript(render 'joint_form') %>",
	btn: ['立即申请', '暂存'],
  yes: function(){
		if ($('#agreement_accepted').is(':checked')){
			$('#project_commit_status').val('applying');
    	$('.save_btn').click();
		}else{
			layer.msg('请阅读并勾选协议！');
			return false;
		}

  },
	btn2: function(){
		if ($('#agreement_accepted').is(':checked')){
			$('#project_commit_status').val('s_store');
    	$('.save_btn').click();
		}else{
			layer.msg('请阅读并勾选协议！');
			return false;
		}
		return false;
	}
	,success: function(layero, index){
	}
});
