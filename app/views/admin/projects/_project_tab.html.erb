<% request_fullpath = request.fullpath %>
<% p request_fullpath %>
<div class="layui-tab layui-tabs-card">
  <ul class="layui-tabs-header">
    <li class="<%= 'layui-this' if request_fullpath =~ /projects/ %>" ><a href="/admin/projects/<%= @project.id %>">详情</a></li>

    <!-- 联合开发案: 任务（任务、缺陷、需求）、工单（缺陷、需求）
    技术支持案: 工单（缺陷、需求）
    内部方案: 任务、缺陷 -->
    <% if @project.p_joint? || @project.p_internal? %>
      <li class="<%= 'layui-this' if request_fullpath =~ /plans/ %>" ><a href="/admin/plans?project_id=<%= @project.id %>">计划</a></li>
    <% end %>

    <% if @project.p_support? || @project.p_joint? %>
      <li class="<%= 'layui-this' if request_fullpath =~ /project_bug_work/ %>" ><a href="<%= project_bug_works_admin_work_orders_path(project_id: @project.id) %>">缺陷</a></li>
      <li class="<%= 'layui-this' if request_fullpath =~ /project_demand_works/ %>" ><a href="<%= project_demand_works_admin_work_orders_path(project_id: @project.id) %>">需求</a></li>
    <% elsif @project.p_internal? %>
      <li class="<%= 'layui-this' if request_fullpath =~ /project_bug_work/ %>" ><a href="<%= project_bug_works_admin_work_orders_path(project_id: @project.id) %>">缺陷</a></li>
    <% end %>

    <% if @project.project_power_keys(current_user_id).include?('set_member') || current_user.is_admin? %>
      <li class="<%= 'layui-this' if request_fullpath =~ /project_users/ %>" ><a href="/admin/project_users?project_id=<%= @project.id %>">成员</a></li>
    <% end %>
    <% if @project.project_power_keys(current_user_id).include?('set_organization') || current_user.is_admin? %>
      <li class="<%= 'layui-this' if request_fullpath =~ /project_organizations/ %>" ><a href="/admin/project_organizations?project_id=<%= @project.id %>">参与企业</a></li>
    <% end %>
  </ul>
</div>
